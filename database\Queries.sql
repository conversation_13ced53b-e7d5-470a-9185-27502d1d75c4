-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Customers table with optimized indexes
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(10),
    dob DATE,
    add1 VARCHAR(255),
    add2 VARCHAR(255),
    street VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    pincode VARCHAR(20),
    phone_number VARCHAR(20) NOT NULL UNIQUE, -- Unique constraint for business logic
    email VARCHAR(255),
    preferred_contact_method VARCHAR(10) DEFAULT 'phone' CHECK (preferred_contact_method IN ('phone', 'email', 'sms')),
    communication_consent BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Critical indexes for customers
CREATE INDEX idx_customers_phone ON customers(phone_number); -- Most frequent lookup
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_created_at ON customers(created_at);

-- Skills table
CREATE TABLE skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_skills_name ON skills(name);

-- Staffs table with performance optimizations
CREATE TABLE staffs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    role VARCHAR(50) NOT NULL,
    contact_ph VARCHAR(20),
    contact_email VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    location VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for staff queries
CREATE INDEX idx_staffs_status ON staffs(status);
CREATE INDEX idx_staffs_role ON staffs(role);
CREATE INDEX idx_staffs_location ON staffs(location);

-- Junction table for staff skills
CREATE TABLE staff_skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    staff_id UUID NOT NULL REFERENCES staffs(id) ON DELETE CASCADE,
    skill_id UUID NOT NULL REFERENCES skills(id) ON DELETE CASCADE,
    proficiency_level VARCHAR(20) DEFAULT 'intermediate' CHECK (proficiency_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(staff_id, skill_id) -- Prevent duplicate skill assignments
);

CREATE INDEX idx_staff_skills_staff_id ON staff_skills(staff_id);
CREATE INDEX idx_staff_skills_skill_id ON staff_skills(skill_id);

-- Services table with enhanced structure
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    duration_minutes INTEGER NOT NULL CHECK (duration_minutes > 0),
    benefits TEXT, -- Using TEXT instead of formatted text for simplicity
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);


CREATE TABLE service_skills (
    "id" UUID NOT NULL PRIMARY KEY,
    "service_id" UUID NOT NULL REFERENCES "services" ("id") ON DELETE CASCADE,
    "skill_id" UUID NOT NULL REFERENCES "skills" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_service_ski_service_cad66c" UNIQUE ("service_id", "skill_id")
);

-- Indexes for service lookups
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_services_skill_id ON services(required_skill_id);
CREATE INDEX idx_services_name ON services(name);

-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(50),
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    stock_quantity INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_products_type ON products(type);
CREATE INDEX idx_products_status ON products(status);

-- Service packages table with JSONB for flexibility
CREATE TABLE service_packages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    services_included JSONB NOT NULL, -- Array of service IDs
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    total_duration_minutes INTEGER NOT NULL CHECK (total_duration_minutes > 0),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_service_packages_status ON service_packages(status);
-- GIN index for JSONB queries
CREATE INDEX idx_service_packages_services ON service_packages USING GIN (services_included);

-- Appointments table with critical performance optimizations
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    staff_id UUID NOT NULL REFERENCES staffs(id) ON DELETE RESTRICT, -- Don't allow deleting staff with appointments
    service_id UUID REFERENCES services(id), -- NULL if package appointment
    package_id UUID REFERENCES service_packages(id), -- NULL if service appointment
    appointment_type VARCHAR(10) NOT NULL CHECK (appointment_type IN ('service', 'package')),
    appointment_date TIMESTAMP WITH TIME ZONE NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    notes TEXT,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure either service_id or package_id is set, but not both
    CONSTRAINT chk_service_or_package CHECK (
        (service_id IS NOT NULL AND package_id IS NULL) OR 
        (service_id IS NULL AND package_id IS NOT NULL)
    )
);

-- Critical indexes for appointment queries
CREATE INDEX idx_appointments_customer_id ON appointments(customer_id);
CREATE INDEX idx_appointments_staff_id ON appointments(staff_id);
CREATE INDEX idx_appointments_date ON appointments(appointment_date);
CREATE INDEX idx_appointments_start_time ON appointments(start_time);
CREATE INDEX idx_appointments_status ON appointments(status);
-- Composite index for availability checking (most important query)
CREATE INDEX idx_appointments_staff_time_status ON appointments(staff_id, start_time, end_time, status);
-- Index for customer appointment history
CREATE INDEX idx_appointments_customer_date ON appointments(customer_id, appointment_date DESC);

-- Recordings table for AI conversation storage
CREATE TABLE recordings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE CASCADE,
    file_path VARCHAR(500) NOT NULL, -- S3 path or URL
    purpose VARCHAR(100),
    summary TEXT,
    transcript JSONB, -- Using JSONB for better performance and querying
    duration_minutes DECIMAL(8,2) CHECK (duration_minutes >= 0),
    call_quality_score INTEGER CHECK (call_quality_score BETWEEN 1 AND 10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for recording queries
CREATE INDEX idx_recordings_customer_id ON recordings(customer_id);
CREATE INDEX idx_recordings_appointment_id ON recordings(appointment_id);
CREATE INDEX idx_recordings_created_at ON recordings(created_at DESC);
-- GIN index for transcript search
CREATE INDEX idx_recordings_transcript ON recordings USING GIN (transcript);

-- Staff availability table (for future expansion)
CREATE TABLE staff_availability (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    staff_id UUID NOT NULL REFERENCES staffs(id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6), -- 0 = Sunday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(staff_id, day_of_week, start_time, end_time)
);

CREATE INDEX idx_staff_availability_staff_day ON staff_availability(staff_id, day_of_week);

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at columns
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_staffs_updated_at BEFORE UPDATE ON staffs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_packages_updated_at BEFORE UPDATE ON service_packages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON appointments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_recordings_updated_at BEFORE UPDATE ON recordings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Useful views for common queries
CREATE VIEW active_services AS
SELECT * FROM services WHERE status = 'active';

CREATE VIEW active_packages AS
SELECT * FROM service_packages WHERE status = 'active';

CREATE VIEW available_staff AS
SELECT s.*, array_agg(sk.name) as skills
FROM staffs s
LEFT JOIN staff_skills ss ON s.id = ss.staff_id
LEFT JOIN skills sk ON ss.skill_id = sk.id
WHERE s.status = 'active'
GROUP BY s.id;