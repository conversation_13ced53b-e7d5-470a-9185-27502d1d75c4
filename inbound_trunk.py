import asyncio
from livekit import api
from config.settings import settings

async def main():
    livekit_api = api.LiveKitAPI()

    trunk = api.SIPInboundTrunkInfo(
        name = "salon_ai",
        krisp_enabled= True,
        numbers=[settings.TWILIO_PHONE_NUMBER],
    )

    request = api.CreateSIPInboundTrunkRequest(
        trunk=trunk
    )

    trunk = await livekit_api.sip.create_sip_inbound_trunk(request)

    await livekit_api.aclose()

asyncio.run(main())