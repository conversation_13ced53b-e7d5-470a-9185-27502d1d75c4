from config.database import init_db, close_db
from tortoise import Tortoise
import logging

logger = logging.getLogger(__name__)


class DatabaseManager:
    @staticmethod
    async def initialize():
        """Initialize database connection"""
        try:
            await init_db()
            logger.info("Database connection established")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise

    @staticmethod
    async def close():
        """Close database connections"""
        try:
            await close_db()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error closing database: {e}")

    @staticmethod
    async def health_check() -> bool:
        """Check database health"""
        try:
            from models.customer import Customer

            await Customer.all().limit(1)
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
