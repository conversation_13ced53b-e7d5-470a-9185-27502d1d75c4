import json
from datetime import datetime, date

def prepare_for_redis(data_dict):
    """Convert data types that Red<PERSON> doesn't support to Redis-compatible types"""
    redis_dict = {}
    
    for key, value in data_dict.items():
        if value is None:
            redis_dict[key] = ""  
        elif isinstance(value, bool):
            redis_dict[key] = "1" if value else "0"  
        elif isinstance(value, (datetime, date)):
            redis_dict[key] = value.isoformat()  
        elif isinstance(value, (dict, list)):
            redis_dict[key] = json.dumps(value) 
        else:
            redis_dict[key] = str(value)  
            
    return redis_dict

def restore_from_redis(redis_dict):
    """Convert Redis data back to proper Python types"""
    restored_dict = {}
    
    for key, value in redis_dict.items():

        if isinstance(key, bytes):
                key = key.decode('utf-8')
                
        if isinstance(value, bytes):
            value = value.decode('utf-8')

        if key.startswith('_'):
            continue
            
        if key == 'communication_consent':
            restored_dict[key] = value == "1"  
        elif key in ['created_at', 'updated_at']:
            try:
                restored_dict[key] = datetime.fromisoformat(value)
            except:
                restored_dict[key] = value
        elif key == 'dob' and value:
            try:
                restored_dict[key] = date.fromisoformat(value)
            except:
                restored_dict[key] = value
        else:
            restored_dict[key] = value
            
    return restored_dict