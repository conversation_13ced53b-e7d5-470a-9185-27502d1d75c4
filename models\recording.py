from tortoise.models import Model
from tortoise import fields
import uuid
from typing import List


class Recording(Model):
    """Audio/video recordings"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    file_path = fields.CharField(max_length=500)
    purpose = fields.CharField(max_length=100, null=True)
    summary = fields.TextField(null=True)
    transcript = fields.JSONField(null=True)
    duration_minutes = fields.DecimalField(max_digits=8, decimal_places=2, null=True)
    call_quality_score = fields.IntField(null=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Foreign key relations
    appointment = fields.ForeignKeyField(
        "models.Appointment", 
        related_name="recordings", 
        null=True,
        on_delete=fields.CASCADE
    )
    customer = fields.ForeignKeyField(
        "models.Customer", 
        related_name="recordings", 
        null=True,
        on_delete=fields.CASCADE
    )
    room = fields.OneToOneField(
        "models.Room", 
        related_name="recording", 
        null=True,
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "recordings"

    @classmethod
    async def create_recording(cls, **data) -> "Recording":
        """Create new Recording"""
        return await cls.create(**data)

    # @classmethod
    # async def get_prev_conversations(cls, customer_id: str) -> List["Recording"]:
    #     """Create new Recording"""
    #     last_recording = (
    #         await cls.filter(customer_id=customer_id).order_by("-created_at").limit(3)
    #     )

    #     if len(last_recording):
    #         return last_recording

    #     return []
