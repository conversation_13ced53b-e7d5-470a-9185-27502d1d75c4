# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Logs
*.log
logs/
salon_ai.log

# Documentation
README.md
readme.md
*.md

# Test files
test_*
*_test.py
tests/

# Temporary files
tmp/
temp/
.tmp/

# Database files (if any local ones)
*.db
*.sqlite
*.sqlite3

# Recordings (these should be in volumes)
recordings/

# Cache
.cache/
.pytest_cache/
