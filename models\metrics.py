from tortoise.models import Model
from tortoise import fields
import uuid
from typing import Optional
import logging


class STTMetrics(Model):
    """Speech-to-text metrics tracking"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    audio_duration = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                       description="Duration (seconds) of audio input received by STT model")
    duration = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                 description="Time (seconds) to create transcript (0 for streaming STT)")
    streamed = fields.BooleanField(default=False, description="True if STT is in streaming mode")
    speech_id = fields.CharField(max_length=255, null=True,
                               description="Unique identifier representing a turn in user input")
    created_at = fields.DatetimeField(auto_now_add=True)

    # Foreign key relations
    session = fields.ForeignKeyField(
        "models.AgentSession",
        related_name="stt_metrics",
        null=True,
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "stt_metrics"
        indexes = [("speech_id",), ("created_at",)]

    def __str__(self):
        return f"STT Metrics - Speech ID: {self.speech_id}, Duration: {self.audio_duration}s"


class LLMMetrics(Model):
    """Large Language Model metrics tracking"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    duration = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                 description="Time (seconds) for LLM to generate entire completion")
    completion_tokens = fields.IntField(null=True,
                                      description="Number of tokens generated by LLM in completion")
    prompt_tokens = fields.IntField(null=True,
                                  description="Number of tokens provided in prompt sent to LLM")
    prompt_cached_tokens = fields.IntField(null=True, default=0,
                                         description="Number of cached tokens in input prompt")
    total_tokens = fields.IntField(null=True, description="Total token usage for completion")
    tokens_per_second = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                          description="Rate of token generation (tokens/second)")
    ttft = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                             description="Time to first token (seconds)")
    speech_id = fields.CharField(max_length=255, null=True,
                               description="Unique identifier representing a turn in user input")
    created_at = fields.DatetimeField(auto_now_add=True)

    # Foreign key relations
    session = fields.ForeignKeyField(
        "models.AgentSession",
        related_name="llm_metrics",
        null=True,
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "llm_metrics"
        indexes = [("speech_id",), ("created_at",)]

    def __str__(self):
        return f"LLM Metrics - Speech ID: {self.speech_id}, Tokens: {self.total_tokens}, Duration: {self.duration}s"


class TTSMetrics(Model):
    """Text-to-speech metrics tracking"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    audio_duration = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                       description="Duration (seconds) of audio output generated by TTS")
    characters_count = fields.IntField(null=True,
                                     description="Number of characters in text input to TTS model")
    duration = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                 description="Time (seconds) for TTS to generate entire audio output")
    ttfb = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                             description="Time to first byte (seconds) of audio output")
    speech_id = fields.CharField(max_length=255, null=True,
                               description="Identifier linking to a user's turn")
    streamed = fields.BooleanField(default=False, description="True if TTS is in streaming mode")
    created_at = fields.DatetimeField(auto_now_add=True)

    # Foreign key relations
    session = fields.ForeignKeyField(
        "models.AgentSession",
        related_name="tts_metrics",
        null=True,
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "tts_metrics"
        indexes = [("speech_id",), ("created_at",)]

    def __str__(self):
        return f"TTS Metrics - Speech ID: {self.speech_id}, Characters: {self.characters_count}, Duration: {self.duration}s"


class EOUMetrics(Model):
    """End-of-utterance metrics tracking"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    end_of_utterance_delay = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                                description="Time from end of speech to turn completion (seconds)")
    transcription_delay = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                            description="Time between end of speech and final transcript (seconds)")
    on_user_turn_completed_delay = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                                      description="Time to execute on_user_turn_completed callback (seconds)")
    speech_id = fields.CharField(max_length=255, null=True,
                               description="Unique identifier indicating the user's turn")
    created_at = fields.DatetimeField(auto_now_add=True)

    # Foreign key relations
    session = fields.ForeignKeyField(
        "models.AgentSession",
        related_name="eou_metrics",
        null=True,
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "eou_metrics"
        indexes = [("speech_id",), ("created_at",)]

    def __str__(self):
        return f"EOU Metrics - Speech ID: {self.speech_id}, EOU Delay: {self.end_of_utterance_delay}s"


class ConversationLatencyMetrics(Model):
    """Calculated conversation latency metrics"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    total_latency = fields.DecimalField(max_digits=10, decimal_places=3, null=True,
                                      description="Total conversation latency (seconds)")
    speech_id = fields.CharField(max_length=255, null=True,
                               description="Unique identifier for the conversation turn")
    created_at = fields.DatetimeField(auto_now_add=True)

    # Foreign key relations to component metrics
    eou_metrics = fields.ForeignKeyField(
        "models.EOUMetrics",
        related_name="conversation_latency",
        null=True,
        on_delete=fields.CASCADE
    )
    llm_metrics = fields.ForeignKeyField(
        "models.LLMMetrics",
        related_name="conversation_latency",
        null=True,
        on_delete=fields.CASCADE
    )
    tts_metrics = fields.ForeignKeyField(
        "models.TTSMetrics",
        related_name="conversation_latency",
        null=True,
        on_delete=fields.CASCADE
    )
    session = fields.ForeignKeyField(
        "models.AgentSession",
        related_name="conversation_latency_metrics",
        null=True,
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "conversation_latency_metrics"
        indexes = [("speech_id",), ("created_at",)]

    def __str__(self):
        return f"Conversation Latency - Speech ID: {self.speech_id}, Total: {self.total_latency}s"

    @classmethod
    async def calculate_and_store(cls, speech_id: str, session_id: Optional[str] = None):
        """Calculate total latency from component metrics and store"""
        try:
            # Get component metrics for this speech_id
            eou = await EOUMetrics.filter(speech_id=speech_id).first()
            llm = await LLMMetrics.filter(speech_id=speech_id).first()
            tts = await TTSMetrics.filter(speech_id=speech_id).first()

            if not all([eou, llm, tts]):
                logging.warning(f"Missing required metrics for conversation latency calculation for speech_id {speech_id}")
                return None

            # Calculate total latency: eou.end_of_utterance_delay + llm.ttft + tts.ttfb
            total_latency = (
                float(eou.end_of_utterance_delay or 0) +
                float(llm.ttft or 0) +
                float(tts.ttfb or 0)
            )

            # Check if a record already exists
            existing = await cls.filter(speech_id=speech_id).first()
            if existing:
                # Update existing record
                existing.total_latency = total_latency
                existing.eou_metrics = eou
                existing.llm_metrics = llm
                existing.tts_metrics = tts
                existing.session_id = session_id
                await existing.save()
                return existing

            # Create and save the latency record
            latency_record = await cls.create(
                total_latency=total_latency,
                speech_id=speech_id,
                eou_metrics=eou,
                llm_metrics=llm,
                tts_metrics=tts,
                session_id=session_id
            )

            return latency_record
        except Exception as e:
            logging.error(f"Error calculating conversation latency: {e}")
            return None


class AgentSession(Model):
    """Agent session tracking for grouping metrics"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    session_name = fields.CharField(max_length=255, null=True)
    started_at = fields.DatetimeField(auto_now_add=True)
    ended_at = fields.DatetimeField(null=True)
    duration_minutes = fields.DecimalField(max_digits=10, decimal_places=2, null=True)
    total_turns = fields.IntField(default=0, description="Total conversation turns in session")
    status = fields.CharField(max_length=50, default="active")

    # Foreign key relations
    room = fields.ForeignKeyField(
        "models.Room",
        related_name="agent_sessions",
        null=True,
        on_delete=fields.CASCADE
    )

    # Reverse relations
    stt_metrics: fields.ReverseRelation["STTMetrics"]
    llm_metrics: fields.ReverseRelation["LLMMetrics"]
    tts_metrics: fields.ReverseRelation["TTSMetrics"]
    eou_metrics: fields.ReverseRelation["EOUMetrics"]
    conversation_latency_metrics: fields.ReverseRelation["ConversationLatencyMetrics"]
    usage_summaries: fields.ReverseRelation["UsageSummary"]

    class Meta:
        table = "agent_sessions"
        indexes = [("status",), ("started_at",)]

    def __str__(self):
        return f"Agent Session {self.id} - {self.session_name or 'Unnamed'}"

    async def get_average_latency(self) -> Optional[float]:
        """Calculate average conversation latency for this session"""
        latency_metrics = await self.conversation_latency_metrics.all()
        if not latency_metrics:
            return None

        total_latency = sum(m.total_latency or 0 for m in latency_metrics)
        return float(total_latency / len(latency_metrics))

    async def get_total_tokens_used(self) -> dict:
        """Get total token usage for this session"""
        llm_metrics = await self.llm_metrics.all()

        total_prompt_tokens = sum(m.prompt_tokens or 0 for m in llm_metrics)
        total_completion_tokens = sum(m.completion_tokens or 0 for m in llm_metrics)
        total_tokens = sum(m.total_tokens or 0 for m in llm_metrics)

        return {
            "prompt_tokens": total_prompt_tokens,
            "completion_tokens": total_completion_tokens,
            "total_tokens": total_tokens
        }


class UsageSummary(Model):
    """Aggregated usage metrics for cost estimation"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    total_llm_tokens = fields.IntField(default=0)
    total_llm_prompt_tokens = fields.IntField(default=0)
    total_llm_completion_tokens = fields.IntField(default=0)
    total_tts_characters = fields.IntField(default=0)
    total_stt_audio_duration = fields.DecimalField(max_digits=10, decimal_places=3, default=0)
    total_conversation_turns = fields.IntField(default=0)
    average_latency = fields.DecimalField(max_digits=10, decimal_places=3, null=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Foreign key relations
    session = fields.ForeignKeyField(
        "models.AgentSession",
        related_name="usage_summaries",
        null=True,
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "usage_summaries"
        indexes = [("created_at",)]

    def __str__(self):
        return f"Usage Summary - Session: {self.session_id}, Tokens: {self.total_llm_tokens}"

    @classmethod
    async def generate_for_session(cls, session_id: str):
        """Generate usage summary for a session"""
        session = await AgentSession.get(id=session_id)

        # Get all metrics for the session
        llm_metrics = await session.llm_metrics.all()
        tts_metrics = await session.tts_metrics.all()
        stt_metrics = await session.stt_metrics.all()
        latency_metrics = await session.conversation_latency_metrics.all()

        # Calculate totals
        total_llm_tokens = sum(m.total_tokens or 0 for m in llm_metrics)
        total_llm_prompt_tokens = sum(m.prompt_tokens or 0 for m in llm_metrics)
        total_llm_completion_tokens = sum(m.completion_tokens or 0 for m in llm_metrics)
        total_tts_characters = sum(m.characters_count or 0 for m in tts_metrics)
        total_stt_audio_duration = sum(m.audio_duration or 0 for m in stt_metrics)
        total_conversation_turns = len(set(m.speech_id for m in llm_metrics if m.speech_id))

        # Calculate average latency
        average_latency = None
        if latency_metrics:
            total_latency = sum(m.total_latency or 0 for m in latency_metrics)
            average_latency = total_latency / len(latency_metrics)

        # Create or update summary
        summary, created = await cls.get_or_create(
            session=session,
            defaults={
                "total_llm_tokens": total_llm_tokens,
                "total_llm_prompt_tokens": total_llm_prompt_tokens,
                "total_llm_completion_tokens": total_llm_completion_tokens,
                "total_tts_characters": total_tts_characters,
                "total_stt_audio_duration": total_stt_audio_duration,
                "total_conversation_turns": total_conversation_turns,
                "average_latency": average_latency,
            }
        )

        if not created:
            # Update existing summary
            summary.total_llm_tokens = total_llm_tokens
            summary.total_llm_prompt_tokens = total_llm_prompt_tokens
            summary.total_llm_completion_tokens = total_llm_completion_tokens
            summary.total_tts_characters = total_tts_characters
            summary.total_stt_audio_duration = total_stt_audio_duration
            summary.total_conversation_turns = total_conversation_turns
            summary.average_latency = average_latency
            await summary.save()

        return summary
