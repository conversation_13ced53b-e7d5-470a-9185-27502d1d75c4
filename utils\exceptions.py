class SalonAIException(Exception):
    """Base exception for salon AI application"""
    pass


class CustomerNotFound(SalonAIException):
    """Raised when customer is not found"""

    pass


class CustomerAlreadyExists(SalonAIException):
    """Raised when trying to create duplicate customer"""
    pass


class AppointmentConflict(SalonAIException):
    """Raised when appointment time conflicts"""

    pass


class ServiceNotAvailable(SalonAIException):
    """Raised when requested service is not available"""

    pass
