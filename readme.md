# Salon AI Bot

This application uses a PostgreSQL database and includes seed scripts to initialize sample data.

---

## 🚀 Getting Started

### 1. Clone the Repository

```bash
git clone https://github.com/apoorvmintriem/voice-agent
cd voice-agent
```

### 2. Start PostgreSQL with Docker

Before running the application, start the PostgreSQL database using Docker Compose:

```bash
docker compose up -d
```

This will spin up a PostgreSQL container in the background.

### 3. Seed the Database

Once the database is up and running, populate it with initial data:

```bash
python seed.py
```

### 4. Run the Application

Now you can run the main application:

```bash
python main.py dev
```

---

## 📦 Requirements

- Python 3.8+
- Docker & Docker Compose
- Dependencies listed in `requirements.txt`
---
