from tortoise.models import Model
from tortoise import fields
import uuid
from .appointment import Appointment
from typing import List
from .customer import CustomerNote, CustomerPreference
from .service import ServiceSkill


class Skill(Model):
    """Skills that staff members can have"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(max_length=100, unique=True)
    description = fields.TextField(null=True)
    created_at = fields.DatetimeField(auto_now_add=True)

    # Reverse relations
    service_skills: fields.ReverseRelation["ServiceSkill"]
    staff_skills: fields.ReverseRelation["StaffSkill"]

    class Meta:
        table = "skills"

    def __str__(self):
        return f"skill_id={self.id}, skill name={self.name}, description={self.description}"

    @classmethod
    def get_skills(cls):
        skills = cls.all()
        return skills

    class Meta:
        table = "skills"


class Staff(Model):
    """Staff/employee information"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.Char<PERSON>ield(max_length=100)
    role = fields.Char<PERSON><PERSON>(max_length=50)
    contact_ph = fields.CharField(max_length=20, null=True)
    contact_email = fields.CharField(max_length=255, null=True)
    status = fields.CharField(max_length=20, default="active")
    branch = fields.CharField(max_length=100, null=True)
    notes = fields.TextField(null=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Reverse relations
    appointments: fields.ReverseRelation["Appointment"]
    customer_notes: fields.ReverseRelation["CustomerNote"]
    customer_preferences: fields.ReverseRelation["CustomerPreference"]
    staff_availability: fields.ReverseRelation["StaffAvailability"]
    staff_skills: fields.ReverseRelation["StaffSkill"]

    class Meta:
        table = "staffs"

    def __str__(self):
        return f"staff_id={self.id}, name={self.name}, role={self.role}, notes={self.notes}"

    @classmethod
    async def get_staff(cls) -> List["Staff"]:
        return await cls.all().prefetch_related("staff_skills__skill")

    @classmethod
    async def get_available_staff(cls, skill_ids: List[str]) -> List["Staff"]:
        return (
            await cls.filter(
                status="active",
                staff_skills__skill_id__in=skill_ids,
            )
            .distinct()
            .prefetch_related("staff_skills__skill")
            .limit(5)
        )

    @classmethod
    async def get_staff_details(cls, staff_id: str) -> "Staff":
        staff = (
            await cls.filter(id=staff_id)
            .prefetch_related("staff_skills__skill")
            .first()
        )
        return staff

    async def get_skills(self) -> List[Skill]:
        """Get staff skills"""
        staff_skills = await self.staff_skills.all().prefetch_related("skill")
        return [ss.skill for ss in staff_skills]


class StaffSkill(Model):
    """Staff skills and proficiency levels"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    proficiency_level = fields.CharField(max_length=20, default="intermediate")
    created_at = fields.DatetimeField(auto_now_add=True)

    # Foreign key relations
    skill = fields.ForeignKeyField(
        "models.Skill", related_name="staff_skills", on_delete=fields.CASCADE
    )
    staff = fields.ForeignKeyField(
        "models.Staff", related_name="staff_skills", on_delete=fields.CASCADE
    )

    class Meta:
        table = "staff_skills"
        unique_together = (("staff", "skill"),)

    def __str__(self):
        return f"{self.staff.name if self.staff else 'N/A'} - {self.skill.name if self.skill else 'N/A'} ({self.proficiency_level})"


class StaffAvailability(Model):
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    staff = fields.ForeignKeyField(
        "models.Staff", related_name="availability", on_delete=fields.CASCADE
    )
    day_of_week = fields.IntField()  # 0-6, Sunday = 0
    start_time = fields.TimeField()
    end_time = fields.TimeField()
    is_available = fields.BooleanField(default=True)
    created_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "staff_availability"
        unique_together = (("staff", "day_of_week", "start_time", "end_time"),)
