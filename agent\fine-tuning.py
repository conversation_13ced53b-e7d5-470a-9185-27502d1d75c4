import json
import random
from typing import List, Dict
import asyncio
from models import Staff, Service


class AppointmentDatasetGenerator:
    def __init__(self):
        self.common_questions = [
            "What services do you offer?",
            "How much does a massage cost?",
            "Who are your staff members?",
            "Can I book an appointment?",
            "What are your hours?",
            "What skills do your therapists have?",
            "How long does a facial take?",
            "Can I reschedule my appointment?",
            "What payment methods do you accept?",
        ]
        self.tools = [
            {
                "type": "function",
                "function": {
                    "name": "get_services",
                    "description": "Retrieve list of available services with details",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "service_type": {"type": "string", "description": "Filter by service type (optional)"},
                            "status": {"type": "string", "enum": ["active", "inactive"], "default": "active"}
                        }
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_staff",
                    "description": "Retrieve list of staff members with their skills and availability",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "skill_required": {"type": "string", "description": "Filter staff by required skill"},
                            "status": {"type": "string", "enum": ["active", "inactive"], "default": "active"}
                        }
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "check_availability",
                    "description": "Check staff availability for specific date and time",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "staff_id": {"type": "string", "description": "ID of the staff member"},
                            "date": {"type": "string", "format": "date", "description": "Date in YYYY-MM-DD format"},
                            "start_time": {"type": "string", "format": "time", "description": "Start time in HH:MM format"},
                            "duration_minutes": {"type": "integer", "description": "Duration of the appointment in minutes"}
                        },
                        "required": ["date", "start_time", "duration_minutes"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "create_appointment",
                    "description": "Create a new appointment",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "customer_phone": {"type": "string", "description": "Customer's phone number"},
                            "customer_name": {"type": "string", "description": "Customer's name"},
                            "customer_email": {"type": "string", "description": "Customer's email (optional)"},
                            "service_id": {"type": "string", "description": "ID of the service (required if not package)"},
                            "package_id": {"type": "string", "description": "ID of the package (required if not service)"},
                            "staff_id": {"type": "string", "description": "ID of the assigned staff member"},
                            "appointment_date": {"type": "string", "format": "date", "description": "Date in YYYY-MM-DD format"},
                            "start_time": {"type": "string", "format": "time", "description": "Start time in HH:MM format"},
                            "notes": {"type": "string", "description": "Additional notes or preferences"}
                        },
                        "required": ["customer_phone", "customer_name", "appointment_date", "start_time", "staff_id"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "find_customer",
                    "description": "Find existing customer by phone or email",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "phone": {"type": "string", "description": "Customer's phone number"},
                            "email": {"type": "string", "description": "Customer's email address"}
                        }
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_customer_appointments",
                    "description": "Get customer's appointment history and upcoming appointments",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "customer_id": {"type": "string", "description": "Customer's ID"},
                            "status": {"type": "string", "enum": ["scheduled", "completed", "cancelled", "all"], "default": "all"}
                        },
                        "required": ["customer_id"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "update_appointment",
                    "description": "Update or reschedule an existing appointment",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "appointment_id": {"type": "string", "description": "ID of the appointment to update"},
                            "new_date": {"type": "string", "format": "date", "description": "New date in YYYY-MM-DD format"},
                            "new_start_time": {"type": "string", "format": "time", "description": "New start time in HH:MM format"},
                            "new_staff_id": {"type": "string", "description": "New staff member ID (optional)"},
                            "status": {"type": "string", "enum": ["scheduled", "confirmed", "cancelled"], "description": "New status"},
                            "notes": {"type": "string", "description": "Updated notes"}
                        },
                        "required": ["appointment_id"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_packages",
                    "description": "Retrieve available service packages",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "status": {"type": "string", "enum": ["active", "inactive"], "default": "active"}
                        }
                    }
                }
            }
        ]

    @classmethod
    async def create(cls):
        self.services = await Service.get_active_services()
        self.staff = await Staff.get_available_staff()
        return cls()

    def generate_service_inquiry_examples(self) -> List[Dict]:
        examples = []

        # General service inquiry
        examples.append(
            {
                "input": {
                    "messages": [
                        {"role": "user", "content": "What services do you offer?"}
                    ],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": f"We offer a comprehensive range of wellness and beauty services including:\n\n"
                        + "\n".join(
                            [
                                f"• {i+1}{service['name']} ({service['duration']} minutes) - ${service['amount']:.2f}"
                                for i,service in enumerate(self.services[:4])
                            ]
                        ),
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": "We have many services available. Please check our website for details.",
                    }
                ],
            }
        )

        service = random.choice(self.services)
        examples.append(
            {
                "input": {
                    "messages": [
                        {"role": "user", "content": f"Tell me about {service['name']}"}
                    ],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": f"{service['name']} is one of our popular services. It takes {service['duration']} minutes and costs ${service['amount']:.2f}. This service requires expertise in {', '.join(service['skills'])}. Our qualified therapists who can perform this service are skilled in these areas. Would you like to book this service or learn about our available time slots?",
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": f"We offer {service['name']} for ${service['amount']:.2f}.",
                    }
                ],
            }
        )

        return examples

    def generate_staff_inquiry_examples(self) -> List[Dict]:
        examples = []

        # General staff inquiry
        examples.append(
            {
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": "Who are your therapists and what are their specialties?",
                        }
                    ],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": "Our experienced team includes:\n\n"
                        + "\n".join(
                            [
                                f"• {staff['name']} - Specializes in {', '.join(staff['skills'])} (Located in {staff['location']})"
                                for staff in self.staff[:4]
                            ]
                        )
                        + f"\n\nAnd {len(self.staff)-4} more skilled professionals. Each therapist is carefully selected for their expertise and experience. Would you like to book with a specific therapist or shall I recommend someone based on the service you're interested in?",
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": "We have several therapists with different skills.",
                    }
                ],
            }
        )

        # Specific staff inquiry
        staff_member = random.choice(self.staff)
        examples.append(
            {
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": f"What can you tell me about {staff_member['name']}?",
                        }
                    ],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": f"{staff_member['name']} is one of our skilled therapists specializing in {', '.join(staff_member['skills'])}. {staff_member['name']} works from our {staff_member['location']} and is available for services that require these specializations. Would you like to check {staff_member['name']}'s availability or book an appointment?",
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": f"{staff_member['name']} works here and has various skills.",
                    }
                ],
            }
        )

        return examples

    def generate_booking_examples(self) -> List[Dict]:
        examples = []

        # Booking inquiry
        examples.append(
            {
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": "I'd like to book an appointment for a massage",
                        }
                    ],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": "I'd be happy to help you book a massage appointment! We offer several massage options:\n\n• Swedish Massage (90 minutes) - $150.00 - Great for relaxation\n• Hot Stone Massage (120 minutes) - $200.00 - Deep relaxation with heated stones\n\nOur massage therapists include Michael Chen and David Rodriguez, both highly skilled in various massage techniques. \n\nWhich type of massage interests you, and do you have a preferred date and time? I'll also need your contact information to complete the booking.",
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": "Sure, we can book you a massage. What time works for you?",
                    }
                ],
            }
        )

        # Package booking inquiry
        package = random.choice(self.packages)
        examples.append(
            {
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": f"I'm interested in the {package['name']}",
                        }
                    ],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": f"Excellent choice! The {package['name']} includes {', '.join(package['services'])} and takes approximately {package['duration']} minutes total. The package price is ${package['amount']:.2f}, which offers great value compared to booking services individually.\n\nThis comprehensive package requires coordination between our specialized staff members. Would you like to check availability for this package? I'll need your preferred date and contact information to proceed with the booking.",
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": f"The {package['name']} costs ${package['amount']:.2f}. Want to book it?",
                    }
                ],
            }
        )

        return examples

    def generate_pricing_examples(self) -> List[Dict]:
        examples = []

        # General pricing inquiry
        examples.append(
            {
                "input": {
                    "messages": [{"role": "user", "content": "What are your prices?"}],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": "Here's our current pricing structure:\n\n**Individual Services:**\n"
                        + "\n".join(
                            [
                                f"• {service['name']}: ${service['amount']:.2f} ({service['duration']} min)"
                                for service in self.services[:5]
                            ]
                        )
                        + "\n\n**Package Deals:**\n"
                        + "\n".join(
                            [
                                f"• {package['name']}: ${package['amount']:.2f} ({package['duration']} min total)"
                                for package in self.packages
                            ]
                        )
                        + "\n\nPackages offer significant savings compared to individual bookings. Would you like more details about any specific service or package?",
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": "Our prices vary depending on the service. Please check our price list.",
                    }
                ],
            }
        )

        return examples

    def generate_availability_examples(self) -> List[Dict]:
        examples = []

        # Availability inquiry
        examples.append(
            {
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": "What's your availability this week?",
                        }
                    ],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": "I'd be happy to check our availability for you! To provide the most accurate scheduling options, I'll need to know:\n\n1. Which service or package you're interested in\n2. Your preferred therapist (if any)\n3. Your preferred time of day (morning, afternoon, evening)\n4. Any specific days that work best for you\n\nOnce I have these details, I can check our real-time availability and offer you the best time slots. Different services require different therapists and durations, so this information helps me give you precise options.",
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": "We have some availability this week. What service do you want?",
                    }
                ],
            }
        )

        return examples

    def generate_rescheduling_examples(self) -> List[Dict]:
        examples = []

        # Rescheduling request
        examples.append(
            {
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": "I need to reschedule my appointment",
                        }
                    ],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": "I'll be happy to help you reschedule your appointment. To locate your booking and offer you the best alternative times, I'll need:\n\n1. Your phone number or email address used for the original booking\n2. The approximate date/time of your current appointment\n3. Your preferred new date and time\n\nOnce I find your appointment, I can check availability with the same therapist or suggest alternative options if needed. We understand that schedules change, and we're flexible with rescheduling when possible.",
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": "Okay, when do you want to reschedule to?",
                    }
                ],
            }
        )

        return examples

    def generate_complex_scenarios(self) -> List[Dict]:
        examples = []

        # Multi-part inquiry
        examples.append(
            {
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": "I want to book a facial and massage for next week, preferably with experienced therapists. What would that cost and how long would it take?",
                        }
                    ],
                    "tools": [],
                    "parallel_tool_calls": True,
                },
                "preferred_output": [
                    {
                        "role": "assistant",
                        "content": "Perfect! I can help you book both a facial and massage. Here are your options:\n\n**Facial Options:**\n• Deep Cleansing Facial (60 min) - $120.00 with Sarah Johnson (expert in Facial Treatments & Skin Analysis)\n• Anti-Aging Treatment (90 min) - $180.00 with Sarah Johnson (Advanced Skincare specialist)\n\n**Massage Options:**\n• Swedish Massage (90 min) - $150.00 with Michael Chen (Massage Therapy expert)\n• Hot Stone Massage (120 min) - $200.00 with Michael Chen (Stone Therapy specialist)\n\n**Total Time:** 150-210 minutes depending on your choices\n**Total Cost:** $270.00-$380.00 for individual services\n\n**Better Value Option:** Our Beauty Boost Package includes Deep Cleansing Facial + Manicure & Pedicure for $190.00, or consider our Relaxation Package with Swedish Massage + Aromatherapy for $220.00.\n\nWould you prefer to book individual services or explore our packages? I can check availability for next week once you decide.",
                    }
                ],
                "non_preferred_output": [
                    {
                        "role": "assistant",
                        "content": "We have facials and massages available. They cost different amounts depending on what you choose.",
                    }
                ],
            }
        )

        return examples

    def generate_complete_dataset(self, num_examples: int = 50) -> List[Dict]:
        """Generate a complete dataset with various conversation scenarios"""
        all_examples = []

        # Generate different types of examples
        all_examples.extend(self.generate_service_inquiry_examples())
        all_examples.extend(self.generate_staff_inquiry_examples())
        all_examples.extend(self.generate_booking_examples())
        all_examples.extend(self.generate_pricing_examples())
        all_examples.extend(self.generate_availability_examples())
        all_examples.extend(self.generate_rescheduling_examples())
        all_examples.extend(self.generate_complex_scenarios())

        # Add more variations by randomly generating similar examples
        while len(all_examples) < num_examples:
            example_type = random.choice(
                [
                    "service_inquiry",
                    "staff_inquiry",
                    "booking",
                    "pricing",
                    "availability",
                    "rescheduling",
                ]
            )

            if example_type == "service_inquiry":
                all_examples.extend(self.generate_service_inquiry_examples())
            elif example_type == "staff_inquiry":
                all_examples.extend(self.generate_staff_inquiry_examples())
            elif example_type == "booking":
                all_examples.extend(self.generate_booking_examples())
            elif example_type == "pricing":
                all_examples.extend(self.generate_pricing_examples())
            elif example_type == "availability":
                all_examples.extend(self.generate_availability_examples())
            elif example_type == "rescheduling":
                all_examples.extend(self.generate_rescheduling_examples())

        return all_examples[:num_examples]

    def save_dataset(
        self,
        examples: List[Dict],
        filename: str = "appointment_system_finetuning_dataset.jsonl",
    ):
        """Save the dataset in JSONL format for OpenAI fine-tuning"""
        with open(filename, "w", encoding="utf-8") as f:
            for example in examples:
                f.write(json.dumps(example, ensure_ascii=False) + "\n")

        print(f"Dataset saved to {filename} with {len(examples)} examples")
        return filename


async def main():
    # Generate the dataset
    generator = await AppointmentDatasetGenerator.create()
    examples = generator.generate_complete_dataset(num_examples=100)

    # Save to file
    filename = generator.save_dataset(examples)

    # Print statistics
    print(f"\nDataset Statistics:")
    print(f"Total examples: {len(examples)}")
    print(
        f"Average preferred response length: {sum(len(ex['preferred_output'][0]['content']) for ex in examples) / len(examples):.0f} characters"
    )
    print(
        f"Average non-preferred response length: {sum(len(ex['non_preferred_output'][0]['content']) for ex in examples) / len(examples):.0f} characters"
    )

    # Show a sample
    print(f"\nSample example:")
    print(json.dumps(examples[0], indent=2, ensure_ascii=False))


if __name__ == "__main__":
    asyncio.run(main())
