from config.logging import setup_logging
from database.connection import DatabaseManager
import logging
from models import Staff, Service, StaffSkill, Appointment
import asyncio
from datetime import datetime

setup_logging()
logger = logging.getLogger(__name__)


class Database:
    """Main application class"""

    def __init__(self):
        self.db_manager = DatabaseManager()

    async def startup(self):
        """Application startup"""
        try:
            await self.db_manager.initialize()
            logger.info("Salon AI Application started successfully")
        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            raise

    async def shutdown(self):
        """Application shutdown"""
        try:
            await self.db_manager.close()
            logger.info("Salon AI Application shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


async def entrypoint():
    try:
        db = Database()
        await db.startup()
        cust_id = "9f5497d7-602e-4574-9ed6-a03c0272399d"
        services_with_skills_required = await Service.get_active_services()

        return_arr = []
        return_str = ""

        for i, service in enumerate(services_with_skills_required):
            return_str = f"{i+1}. ({service} Skills_Required=["
            for i, service_skill in enumerate(service.service_skills):
                skill = service_skill.skill
                return_str += f"{i+1}. ({skill}), "
            return_str += "])"
            return_arr.append(return_str)
        print(" ,".join(return_arr))
        return " ,".join(return_arr)
        # return_str = ""

        # for i, appt in enumerate(appointments):
        #     return_str += f"{i+1}.[Appointment Details - ({appt}), (Service Details - {appt.service}), Staff Details - ({appt.staff})"
        #     if appt.recordings and appt.recordings[0].summary:
        #         summary = appt.recordings[0].summary
        #         return_str += f", Conversation_summary: {summary.strip().replace('**', '').replace('- ', '')}]"

        #     return_str += "\n"

        # for i, service in enumerate(services_with_skills_required):
        #         return_str = f"{i+1}. ({service} Skills_Required=["
        #         for i, service_skill in enumerate(service.service_skills):
        #             skill = service_skill.skill
        #             return_str += f"{i+1}. ({skill}), "
        #         return_str += "])"
        #         return_arr.append(return_str)
    except Exception as e:
        raise e


if __name__ == "__main__":
    asyncio.run(entrypoint())
    # cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, shutdown_process_timeout=100))
