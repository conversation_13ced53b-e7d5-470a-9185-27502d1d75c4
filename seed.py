import asyncio
import csv
from datetime import datetime
from config.logging import setup_logging
from database.connection import DatabaseManager
import logging
import os
from models.customer import Customer
from models.staff import Staff, <PERSON><PERSON>, StaffSkill
from models.service import Service, ServiceSkills

setup_logging()
logger = logging.getLogger(__name__)


BASE_DIR = os.path.dirname(os.path.abspath(__file__))

CUSTOMER_CSV_FILE = os.path.join(BASE_DIR, "data", "Customer_schema.csv")
SKILLS_CSV_FILE = os.path.join(BASE_DIR, "data", "Skills_schema.csv")
STAFF_CSV_FILE = os.path.join(BASE_DIR, "data", "Staffs_schema.csv")
SERVICES_CSV_FILE = os.path.join(BASE_DIR, "data", "Services_schema.csv")
STAFF_SKILL_CSV_FILE = os.path.join(BASE_DIR, "data", "Staff_skill_schema.csv")
SERVICES_SKILL_CSV_FILE = os.path.join(BASE_DIR, "data", "Service_skill_schema.csv")


class Database:
    """Main application class"""

    def __init__(self):
        self.db_manager = DatabaseManager()

    async def startup(self):
        """Application startup"""
        try:
            await self.db_manager.initialize()
            logger.info("Salon AI Application started successfully")
        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            raise

    async def shutdown(self):
        """Application shutdown"""
        try:
            await self.db_manager.close()
            logger.info("Salon AI Application shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


async def init():
    db = Database()
    await db.startup()
    await seed_customers()
    await seed_staff()
    await seed_skills()
    await seed_staff_skills()
    await seed_services()
    await seed_services_skills()
    await db.shutdown()


async def seed_customers():
    with open(CUSTOMER_CSV_FILE, newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        customers = []
        for row in reader:
            row["communication_consent"] = (
                row["communication_consent"].lower() == "true"
            )
            row["dob"] = row["dob"] or None
            if row["dob"]:
                try:
                    row["dob"] = datetime.strptime(row["dob"], "%Y-%m-%d").date()
                except ValueError:
                    row["dob"] = None

            customer = Customer(**row)
            customers.append(customer)

        await Customer.bulk_create(customers)
        print(f"✅ Seeded {len(customers)} customers.")


async def seed_staff():
    with open(STAFF_CSV_FILE, newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        staffs = [Staff(**row) for row in reader]
        await Staff.bulk_create(staffs)
        print(f"✅ Seeded {len(staffs)} staff.")


async def seed_skills():
    with open(SKILLS_CSV_FILE, newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        skills = [Skill(**row) for row in reader]
        await Skill.bulk_create(skills)
        print(f"✅ Seeded {len(skills)} skills.")


async def seed_staff_skills():
    with open(STAFF_SKILL_CSV_FILE, newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        staff_skills = []
        for row in reader:
            if row["staff_id"]:
                staff_skills.append(StaffSkill(**row))
            else:
                print(f"⚠️ Skipping row: {row}")
        await StaffSkill.bulk_create(staff_skills)
        print(f"✅ Seeded {len(staff_skills)} staff skills.")


async def seed_services():
    with open(SERVICES_CSV_FILE, newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        services = [Service(**row) for row in reader]
        await Service.bulk_create(services)
        print(f"✅ Seeded {len(services)} services.")


async def seed_services_skills():
    with open(SERVICES_SKILL_CSV_FILE, newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        service_skills = [ServiceSkills(**row) for row in reader]
        await ServiceSkills.bulk_create(service_skills)
        print(f"✅ Seeded {len(service_skills)} service skills.")


if __name__ == "__main__":
    asyncio.run(init())
