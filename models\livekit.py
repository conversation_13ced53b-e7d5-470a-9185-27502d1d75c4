import uuid
from typing import List, Optional
from tortoise.models import Model
from tortoise import fields
from .recording import Recording


class Room(Model):
    """Rooms for appointments/recordings"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    status = fields.CharField(max_length=100, null=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    started_at = fields.DatetimeField(null=True)
    ended_at = fields.DatetimeField(null=True)
    duration_minutes = fields.DecimalField(
        max_digits=10, decimal_places=2, default=0, null=True
    )

    # Foreign key relations
    participant = fields.ForeignKeyField(
        "models.Customer", related_name="rooms", on_delete=fields.CASCADE
    )

    # Reverse relations
    recording: fields.ReverseRelation["Recording"]

    class Meta:
        table = "rooms"

    def __str__(self):
        return f"Room {self.id} - Participant: {self.participant_id}"

    @classmethod
    async def find_by_id(cls, room_id: str):
        return await cls.filter(id=room_id).first()

    @classmethod
    async def find_by_participant(cls, participant_id: str) -> List["Room"]:
        """Find rooms by participant ID"""
        return (
            await cls.filter(participant_id=participant_id)
            .all()
            .order_by("-created_at")
        )

    @classmethod
    async def all_rooms(cls, participant_id: str) -> List["Room"]:
        """Find rooms by participant ID"""
        return await cls.filter(participant_id=participant_id).all()

    @classmethod
    async def create_with_participant(cls, participant_id: str, **data) -> "Room":
        """Create room with participant"""
        return await cls.create(participant_id=participant_id, **data)

    async def get_participant(self) -> "Customer":
        """Get room participant"""
        return await self.participant
