from tortoise import <PERSON><PERSON><PERSON>
from .settings import settings
import logging

# logger = logging.getLogger(__name__)

# Tortoise ORM Configuration
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": settings.DATABASE_HOST,
                "port": settings.DATABASE_PORT,
                "user": settings.DATABASE_USER,
                "password": settings.DATABASE_PASSWORD,
                "database": settings.DATABASE_DB,
                "minsize": settings.DB_MIN_CONNECTIONS,
                "maxsize": settings.DB_MAX_CONNECTIONS,
                "command_timeout": settings.DB_CONNECTION_TIMEOUT,
            },
        }
    },
    "apps": {
        "models": {
            "models": [
                "models.customer",
                "models.livekit",
                "models.staff",
                "models.service",
                "models.appointment",
                "models.recording",
                "models.metrics",
                "aerich.models",
            ],
            "default_connection": "default",
        },
    },
}


async def init_db():
    """Initialize database connection"""
    try:
        await Tortoise.init(config=TORTOISE_ORM)
        # await Tortoise.generate_schemas()
        # logger.info("Database initialized successfully")
    except Exception as e:
        # logger.error(f"Database initialization failed: {e}")
        raise


async def close_db():
    """Close database connections"""
    await Tortoise.close_connections()
    # logger.info("Database connections closed")
